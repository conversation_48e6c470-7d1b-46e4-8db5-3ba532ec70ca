using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Npgsql;
using Serilog;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Diagnostics
{
    /// <summary>
    /// Diagnostic utility to troubleshoot ApiCallLogs table creation issues
    /// </summary>
    public static class ApiCallLogsTableDiagnostics
    {
        public static async Task RunDiagnosticsAsync()
        {
            // Configure Serilog for detailed logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/apicallogs_diagnosis.log", rollingInterval: RollingInterval.Day)
                .MinimumLevel.Debug()
                .CreateLogger();

            System.Console.WriteLine("🔍 Diagnosing ApiCallLogs table creation issue...");

            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Setup services
                var services = new ServiceCollection();
                services.AddLogging(builder => builder.AddSerilog());

                // Add database context
                var baseConnectionString = configuration.GetConnectionString("MLFeatures") ??
                    "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";

                var optimizedConnectionString = DatabaseConfigurationHelper.CreateOptimizedBuilder(baseConnectionString).ConnectionString;

                services.AddDbContextFactory<MLFeaturesDbContext>(options =>
                {
                    options.UseNpgsql(optimizedConnectionString);
                    options.EnableSensitiveDataLogging(true);
                    options.EnableDetailedErrors(true);
                    options.LogTo(System.Console.WriteLine, LogLevel.Information);
                });

                var serviceProvider = services.BuildServiceProvider();
                var logger = serviceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<Program>>();

                logger.LogInformation("🔧 Starting ApiCallLogs table diagnosis...");

                // Test 1: Basic connection test
                logger.LogInformation("📡 Testing database connection...");
                await TestDatabaseConnection(optimizedConnectionString, logger);

                // Test 2: Check if table exists
                logger.LogInformation("🔍 Checking if ApiCallLogs table exists...");
                await CheckTableExists(optimizedConnectionString, logger);

                // Test 3: Test with Entity Framework
                logger.LogInformation("🔧 Testing with Entity Framework...");
                await TestWithEntityFramework(serviceProvider, logger);

                // Test 4: Manual table creation
                logger.LogInformation("🛠️ Attempting manual table creation...");
                await AttemptManualTableCreation(optimizedConnectionString, logger);

                logger.LogInformation("✅ Diagnosis completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"❌ Diagnosis failed: {ex.Message}");
                System.Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Log.Fatal(ex, "Fatal error during diagnosis");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        static async Task TestDatabaseConnection(string connectionString, Microsoft.Extensions.Logging.ILogger logger)
{
    try
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();
        logger.LogInformation("✅ Database connection successful");
        
        // Test basic query
        using var command = new NpgsqlCommand("SELECT version();", connection);
        var version = await command.ExecuteScalarAsync();
        logger.LogInformation("📊 PostgreSQL version: {Version}", version);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Database connection failed: {Message}", ex.Message);
        throw;
    }
}

        static async Task CheckTableExists(string connectionString, Microsoft.Extensions.Logging.ILogger logger)
{
    try
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();
        
        using var command = new NpgsqlCommand(@"
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ApiCallLogs'
            );", connection);
        
        var exists = (bool)(await command.ExecuteScalarAsync() ?? false);
        
        if (exists)
        {
            logger.LogInformation("✅ ApiCallLogs table already exists");
            
            // Get table structure
            using var structureCommand = new NpgsqlCommand(@"
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'ApiCallLogs' 
                ORDER BY ordinal_position;", connection);
            
            using var reader = await structureCommand.ExecuteReaderAsync();
            logger.LogInformation("📋 Current table structure:");
            while (await reader.ReadAsync())
            {
                logger.LogInformation("  - {Column}: {Type} (Nullable: {Nullable}, Default: {Default})", 
                    reader["column_name"], reader["data_type"], reader["is_nullable"], reader["column_default"]);
            }
        }
        else
        {
            logger.LogInformation("⚠️ ApiCallLogs table does not exist");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Failed to check table existence: {Message}", ex.Message);
        throw;
    }
}

        static async Task TestWithEntityFramework(IServiceProvider serviceProvider, Microsoft.Extensions.Logging.ILogger logger)
{
    try
    {
        var contextFactory = serviceProvider.GetRequiredService<IDbContextFactory<MLFeaturesDbContext>>();
        using var context = await contextFactory.CreateDbContextAsync();
        
        // Check database connection
        var canConnect = await context.Database.CanConnectAsync();
        logger.LogInformation("📡 EF Core can connect: {CanConnect}", canConnect);
        
        // Check pending migrations
        var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
        logger.LogInformation("🔄 Pending migrations: {Count}", pendingMigrations.Count());
        foreach (var migration in pendingMigrations)
        {
            logger.LogInformation("  - {Migration}", migration);
        }
        
        // Try to query the table
        try
        {
            var count = await context.ApiCallLogs.CountAsync();
            logger.LogInformation("✅ ApiCallLogs table accessible, count: {Count}", count);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "⚠️ Cannot access ApiCallLogs table: {Message}", ex.Message);
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Entity Framework test failed: {Message}", ex.Message);
        throw;
    }
}

        static async Task AttemptManualTableCreation(string connectionString, Microsoft.Extensions.Logging.ILogger logger)
{
    try
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();
        
        // Try the exact CREATE TABLE command from the error
        var createTableSql = @"
            CREATE TABLE IF NOT EXISTS ""ApiCallLogs"" (
                ""Id"" bigint GENERATED BY DEFAULT AS IDENTITY,
                ""Timestamp"" timestamp with time zone NOT NULL,
                ""Provider"" character varying(50) NOT NULL,
                ""Operation"" character varying(100) NOT NULL,
                ""Symbol"" character varying(20),
                ""RequestData"" character varying(2000),
                ""ResponseData"" character varying(2000),
                ""Success"" boolean NOT NULL,
                ""StatusCode"" integer,
                ""ErrorMessage"" character varying(500),
                ""DurationMs"" integer NOT NULL,
                ""TokensUsed"" integer,
                ""Cost"" numeric(10,6),
                ""Metadata"" character varying(1000),
                ""CreatedAt"" timestamp with time zone NOT NULL,
                CONSTRAINT ""PK_ApiCallLogs"" PRIMARY KEY (""Id"")
            );";
        
        using var command = new NpgsqlCommand(createTableSql, connection);
        await command.ExecuteNonQueryAsync();
        
        logger.LogInformation("✅ Manual table creation successful");
        
        // Test inserting a record
        var insertSql = @"
            INSERT INTO ""ApiCallLogs"" 
            (""Timestamp"", ""Provider"", ""Operation"", ""Success"", ""DurationMs"", ""CreatedAt"")
            VALUES (@timestamp, @provider, @operation, @success, @duration, @created);";
        
        using var insertCommand = new NpgsqlCommand(insertSql, connection);
        insertCommand.Parameters.AddWithValue("timestamp", DateTime.UtcNow);
        insertCommand.Parameters.AddWithValue("provider", "TEST");
        insertCommand.Parameters.AddWithValue("operation", "Diagnosis Test");
        insertCommand.Parameters.AddWithValue("success", true);
        insertCommand.Parameters.AddWithValue("duration", 1);
        insertCommand.Parameters.AddWithValue("created", DateTime.UtcNow);
        
        await insertCommand.ExecuteNonQueryAsync();
        logger.LogInformation("✅ Test record inserted successfully");
        
        // Clean up test record
        using var deleteCommand = new NpgsqlCommand(@"
            DELETE FROM ""ApiCallLogs"" 
            WHERE ""Provider"" = 'TEST' AND ""Operation"" = 'Diagnosis Test';", connection);
        await deleteCommand.ExecuteNonQueryAsync();
        
        logger.LogInformation("✅ Test record cleaned up");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Manual table creation failed: {Message}", ex.Message);
        
        // Try to get more specific error information
        if (ex is PostgresException pgEx)
        {
            logger.LogError("PostgreSQL Error Details:");
            logger.LogError("  - SqlState: {SqlState}", pgEx.SqlState);
            logger.LogError("  - Severity: {Severity}", pgEx.Severity);
            logger.LogError("  - MessageText: {MessageText}", pgEx.MessageText);
            logger.LogError("  - Detail: {Detail}", pgEx.Detail);
            logger.LogError("  - Hint: {Hint}", pgEx.Hint);
        }
        
        throw;
    }
}
    }
}
