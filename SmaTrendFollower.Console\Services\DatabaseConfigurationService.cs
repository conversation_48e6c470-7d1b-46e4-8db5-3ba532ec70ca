using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for high-performance caching
/// </summary>
public sealed class DatabaseConfigurationService : IDatabaseConfigurationService
{
    private readonly ILogger<DatabaseConfigurationService> _logger;
    private readonly IConfiguration _configuration;

    public DatabaseConfigurationService(ILogger<DatabaseConfigurationService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public void ConfigurePostgreSQL(DbContextOptionsBuilder options, string connectionString)
    {
        // Check if debug mode is enabled for parameter binding issues
        var enableSensitiveLogging = Environment.GetEnvironmentVariable("DEBUG_PARAMETER_BINDING")?.ToLowerInvariant() == "true";

        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            // Enable command timeout for long-running operations
            npgsqlOptions.CommandTimeout(60); // Increased from 30 to handle network delays

            // Enable retry on failure for better reliability - Enhanced for socket errors
            npgsqlOptions.EnableRetryOnFailure(
                maxRetryCount: 5, // Increased from 3 for better resilience
                maxRetryDelay: TimeSpan.FromSeconds(10), // Increased from 5 for network recovery
                errorCodesToAdd: new[]
                {
                    // Add specific PostgreSQL error codes for network issues
                    "08000", // Connection exception
                    "08003", // Connection does not exist
                    "08006", // Connection failure
                    "08001", // Unable to connect to data source
                    "08004", // Data source rejected establishment of connection
                    "57P01", // Admin shutdown
                    "57P02", // Crash shutdown
                    "57P03", // Cannot connect now
                    // Add parameter binding related error codes
                    "22P02", // Invalid text representation
                    "22P03", // Invalid binary representation
                    "08P01"  // Protocol violation
                });
        })
        .EnableSensitiveDataLogging(enableSensitiveLogging) // Enable only when debugging
        .EnableServiceProviderCaching(true) // Cache service provider for better performance
        .EnableDetailedErrors(true) // Enable for better parameter binding diagnostics
        .LogTo(message =>
        {
            // Enhanced logging for parameter binding issues
            if (message.Contains("Parameters") || message.Contains("DbCommand") || message.Contains("Failed executing"))
            {
                _logger.LogWarning("EF Core Parameter Issue: {Message}", message);
            }
            else if (enableSensitiveLogging && (message.Contains("@") || message.Contains("parameter")))
            {
                _logger.LogInformation("EF Core Debug: {Message}", message);
            }
            else
            {
                _logger.LogDebug("EF Core: {Message}", message);
            }
        }, LogLevel.Debug);

        // Configure query behavior for performance
        options.ConfigureWarnings(warnings =>
        {
            warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
        });
    }

    public string GetOptimizedPostgreSQLConnectionString(string baseConnectionString)
    {
        // Use the standardized configuration from the helper
        return DatabaseConfigurationHelper.CreateOptimizedBuilder(baseConnectionString).ConnectionString;
    }



    public async Task OptimizePostgreSQLAsync(string connectionString)
    {
        try
        {
            _logger.LogInformation("Starting PostgreSQL optimization");

            using var connection = new Npgsql.NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            // Execute PostgreSQL optimization commands
            var optimizationCommands = new[]
            {
                "ANALYZE;", // Update table statistics for query optimizer
                "VACUUM ANALYZE;", // Reclaim space and update statistics
            };

            foreach (var command in optimizationCommands)
            {
                try
                {
                    using var cmd = new Npgsql.NpgsqlCommand(command, connection);
                    await cmd.ExecuteNonQueryAsync();
                    _logger.LogDebug("Executed PostgreSQL optimization command: {Command}", command);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to execute PostgreSQL optimization command: {Command}", command);
                }
            }

            _logger.LogInformation("PostgreSQL optimization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during PostgreSQL optimization");
            throw;
        }
    }
}
